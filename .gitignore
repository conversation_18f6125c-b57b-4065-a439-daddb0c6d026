# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# Electron build artifacts
dist-electron/
out/
build/
release/

# Electron executables and installers
*.exe
*.dmg
*.pkg
*.deb
*.rpm
*.AppImage
*.snap

# Windows specific
*.dll
*.pdb
*.lib

# macOS specific
*.app/
*.dsym/

# Electron cache and temp files
.electron-builder-cache/
electron-builder-effective-config.yaml

# Node modules (if not already there)
node_modules/

# Build outputs
dist/
build/
out/

# Electron Forge
.webpack/
